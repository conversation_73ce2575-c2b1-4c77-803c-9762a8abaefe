<?php

// Generator API - chat message handling (demo)
// Accepts chat messages and optional context; authenticates via JWT; returns static response for now

require_once 'utils.php';
require_once 'models/ResultModel.php';
require_once 'config.php';
require_once 'language_config.php';
$selectedLanguage = getSelectedLanguage();
require_once 'language_strings.php';
require_once 'stripe/StripeLogger.php';

// Optional: forward chat to n8n if webhook URL is configured
// Provide N8N_CHAT_WEBHOOK_URL via environment or define $n8nChatWebhookUrl somewhere central
$n8nManuelInputWebhookUrl = "https://n8n.coinscout.app/webhook/create-portfolio-manuel";
$n8nChatWebhookUrl = "https://n8n.coinscout.app/webhook/create-portfolio-chat-ai";
$n8nHelpWebhookUrl = "https://n8n.coinscout.app/webhook/help";
$n8nHelpProWebhookUrl = "https://n8n.coinscout.app/webhook/help-pro";
if (file_exists(__DIR__ . '/n8n_sender.php')) {
    require_once 'n8n_sender.php';
}
// Simulator for temporary demo responses
if (file_exists(__DIR__ . '/generator_simulator.php')) {
    require_once 'generator_simulator.php';
}
// Temporary flag to use simulator for send_chat; keep real method intact
$USE_N8N_SIMULATOR = true;

// Apply CORS headers
cors_client();

header('Content-Type: application/json');

// N8N portfolio response'unu template formatına dönüştür
function convertN8NToTemplate($portfolioData)
{
    global $link;

    $allocation = [];
    foreach ($portfolioData['assets'] as $asset) {
        // Coin bilgilerini ve skorunu veritabanından çek
        $stmt = mysqli_prepare($link, "SELECT c.name, c.symbol, c.total_score, c.geckoid FROM coindata c WHERE c.id = ?");
        mysqli_stmt_bind_param($stmt, "i", $asset['coin_id']);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $coin = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if ($coin) {
            // Coin kategorisini al
            $categoryStmt = mysqli_prepare($link, "SELECT category_name FROM coindata_categories WHERE geckoid = ? LIMIT 1");
            mysqli_stmt_bind_param($categoryStmt, "s", $coin['geckoid']);
            mysqli_stmt_execute($categoryStmt);
            $categoryResult = mysqli_stmt_get_result($categoryStmt);
            $category = mysqli_fetch_assoc($categoryResult);
            mysqli_stmt_close($categoryStmt);

            $allocation[] = [
                'id' => $asset['coin_id'],
                'symbol' => $coin['symbol'],
                'fullName' => $coin['name'],
                'percentage' => $asset['allocation'],
                'score' => $coin['total_score'] ? (float)$coin['total_score'] : null,
                'description' => ($category ? $category['category_name'] . ' kategorisinde ' : '') . 'AI tarafından seçilen yüksek potansiyelli varlık'
            ];
        }
    }

    return [
        'id' => 'ai-generated-' . strtolower($portfolioData['risk_level']) . '-' . time(),
        'name' => 'AI ' . ucfirst($portfolioData['risk_level']) . ' Risk Portföyü',
        'description' => $portfolioData['risk_level'] . ' risk seviyesinde yatırımcılar için AI tarafından oluşturulmuş portföy stratejisi.',
        'riskLevel' => strtolower($portfolioData['risk_level']),
        'timeframe' => $portfolioData['timeframe'] ?? null,
        'templateName' => $portfolioData['template_name'] ?? null,
        'totalScore' => $portfolioData['metrics']['totalScore'] ?? null,
        'metrics' => $portfolioData['metrics'] ?? null,
        'allocation' => $allocation,
        'strategy' => 'AI algoritması tarafından ' . $portfolioData['template_name'] . ' stratejisi ile oluşturulmuş portföy.',
        'reasoningExplanation' => 'Bu portföy, toplam skor ' . round($portfolioData['metrics']['totalScore'], 2) . ' ile optimize edilmiş kategori çeşitliliği ve risk dağılımı sunuyor.',
        'userContext' => ucfirst($portfolioData['risk_level']) . ' risk toleranslı yatırımcı için AI önerisi'
    ];
}

// Only allow POST for chat
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response = new ErrorResult('Method not allowed');
    $response->send(405);
    return;
}

// Read payload
$payload = file_get_contents('php://input');
$data = json_decode($payload, true);

if ($data === null) {
    $response = new ErrorResult('Invalid Request!');
    $response->send(400);
    return;
}
// Determine endpoint (client.php-like) - f is REQUIRED
$functName = $data['f'] ?? null;
if ($functName === null || $functName === '') {
    // Do not answer requests without f, as requested
    (new ErrorResult('Invalid Request!'))->send(404);
    return;
}
// Authenticate user (optional for this endpoint)
$userId = authenticate_user();

// Supported endpoints in this file
$validEndpoints = [
    'send_chat',
    'send_help',
    'generate_manual_portfolio',
];

if (!in_array($functName, $validEndpoints, true)) {
    $response = new ErrorResult('Invalid Request!');
    $response->send(404);
    return;
}

switch ($functName) {
    case 'send_chat': {
    if (!$userId) {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        (new ErrorResult($clientMessages[$lang]['unauthorized_access'] ?? 'Unauthorized access'))->send(401);
        return;
    }

    $message = isset($data['message']) ? trim((string)$data['message']) : '';
    $conversationUuid = isset($data['conversation_id']) ? trim($data['conversation_id']) : null;

    if ($message === '') {
        (new ErrorResult('message field is required'))->send(400);
        return;
    }

    try {
        global $link;

        // Yeni konuşma oluştur veya UUID’den gerçek ID'yi bul
        if (!$conversationUuid) {
            $stmt = mysqli_prepare($link, "INSERT INTO chat_conversations (user_id) VALUES (?)");
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $conversationId = mysqli_insert_id($link);
            mysqli_stmt_close($stmt);

            // UUID'yi çek (trigger ile oluştu)
            $stmt = mysqli_prepare($link, "SELECT uuid FROM chat_conversations WHERE id = ?");
            mysqli_stmt_bind_param($stmt, "i", $conversationId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $conversationUuid = $row['uuid'] ?? null;
            mysqli_stmt_close($stmt);
        } else {
            // UUID’den gerçek ID'yi bul
            $stmt = mysqli_prepare($link, "SELECT id FROM chat_conversations WHERE uuid = ? AND user_id = ?");
            mysqli_stmt_bind_param($stmt, "si", $conversationUuid, $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            if (!$row) {
                (new ErrorResult('Conversation not found'))->send(404);
                return;
            }
            $conversationId = (int)$row['id'];
            mysqli_stmt_close($stmt);
        }

        // Konuşma kapalı mı kontrol et
        $stmt = mysqli_prepare($link, "SELECT is_closed FROM chat_conversations WHERE id = ? AND user_id = ?");
        mysqli_stmt_bind_param($stmt, "ii", $conversationId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $convRow = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if (!$convRow) {
            (new ErrorResult('Conversation not found'))->send(404);
            return;
        }
        if ((int)$convRow['is_closed'] === 1) {
            global $selectedLanguage, $clientMessages;
            $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
            $closedMsg = $clientMessages[$lang]['conversation_closed'] ?? (
                $lang === 'tr' ? 'Bu konuşma kapatılmıştır. Lütfen yeni bir konuşma başlatın.' : 'This conversation has been closed. Please start a new conversation.'
            );
            (new SuccessResult([
                'response' => $closedMsg,
                'conversation_id' => $conversationUuid,
                'conversation_closed' => true,
            ]))->send(200);
            return;
        }

        // Kullanıcı mesajını kaydet
        $stmt = mysqli_prepare($link, "INSERT INTO chat_messages (conversation_id, sender, message_text) VALUES (?, 'user', ?)");
        mysqli_stmt_bind_param($stmt, "is", $conversationId, $message);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);

        // Önceki mesajları çek ve context oluştur
        $stmt = mysqli_prepare($link, "SELECT sender, message_text FROM chat_messages WHERE conversation_id = ? ORDER BY created_at ASC");
        mysqli_stmt_bind_param($stmt, "i", $conversationId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $messages = mysqli_fetch_all($result, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt);

        $context = [];
        foreach ($messages as $msg) {
            $context[] = [
                'role' => $msg['sender'] === 'user' ? 'user' : 'assistant',
                'content' => $msg['message_text']
            ];
        }

        // N8N'e gönder ve cevabı kaydet (templateData veya text)
        if (!empty($n8nChatWebhookUrl) && class_exists('N8NSender')) {
            $n8n = new N8NSender($n8nChatWebhookUrl);
            $n8nResp = $n8n->sendToN8N('chat_message', [
                'userId' => $userId,
                'message' => $message,
                'context' => $context,
            ]);

            if ($n8nResp['success'] === true) {
                $raw = html_entity_decode($n8nResp['response'], ENT_QUOTES, 'UTF-8');
                $decoded = json_decode($raw, true);

                $responseText = '';
                $templateData = null;

                if (is_array($decoded)) {
                    if (isset($decoded[0]['output']['assets']) && isset($decoded[0]['output']['risk_level'])) {
                        $portfolioData = $decoded[0]['output'];
                        $templateData = convertN8NToTemplate($portfolioData);
                        $responseText = $decoded[0]['output']['response'];
                    } else {
                        $responseText = $decoded['response'] ?? $decoded['reply'] ?? $decoded['message'] ?? (is_string($raw) ? $raw : '');
                        $templateData = isset($decoded['templateData']) && is_array($decoded['templateData']) ? $decoded['templateData'] : null;
                    }
                } else {
                    $responseText = (string)$raw;
                }

                // AI cevabını kaydet
                if ($responseText) {
                    if ($templateData !== null) {
                        $json = json_encode($templateData, JSON_UNESCAPED_UNICODE);
                        $stmt = mysqli_prepare($link, "INSERT INTO chat_messages (conversation_id, sender, message_text, message_type, template_data) VALUES (?, 'ai', ?, 'template', ?)");
                        mysqli_stmt_bind_param($stmt, "iss", $conversationId, $responseText, $json);
                    } else {
                        $stmt = mysqli_prepare($link, "INSERT INTO chat_messages (conversation_id, sender, message_text, message_type) VALUES (?, 'ai', ?, 'text')");
                        mysqli_stmt_bind_param($stmt, "is", $conversationId, $responseText);
                    }
                    mysqli_stmt_execute($stmt);
                    mysqli_stmt_close($stmt);
                }

                // TemplateData varsa konuşmayı kapat
                if ($templateData !== null) {
                    $stmt = mysqli_prepare($link, "UPDATE chat_conversations SET is_closed = 1 WHERE id = ?");
                    mysqli_stmt_bind_param($stmt, "i", $conversationId);
                    mysqli_stmt_execute($stmt);
                    mysqli_stmt_close($stmt);
                }

                $payload = [
                    'response' => $responseText,
                    'conversation_id' => $conversationUuid
                ];
                if ($templateData !== null) {
                    $payload['templateData'] = $templateData;
                    $payload['conversation_closed'] = true;
                }

                (new SuccessResult($payload))->send(200);
                return;
            }
        }

        (new ErrorResult('Service unavailable. Please try again later.'))->send(503);
        return;

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, 'Database error in send_chat', ['error' => $e->getMessage()]);
        (new ErrorResult('Internal server error'))->send(500);
        return;
    }
}


    case 'send_help': {
            $message = isset($data['message']) ? trim((string)$data['message']) : '';
            $conversationId = isset($data['conversation_id']) ? (int)$data['conversation_id'] : null;

            if ($message === '') {
                $response = new ErrorResult('message field is required');
                $response->send(400);
                return;
            }

            // Determine webhook URL based on user subscription level
            $webhookUrl = $n8nHelpWebhookUrl; // Default to free help
            $isPaidUser = false;
            $context = [];

            if ($userId) {
                // Logged-in user: save messages and get context from database
                global $link;

                try {
                    // Create or get conversation for logged-in users
                    if (!$conversationId) {
                        $stmt = mysqli_prepare($link, "INSERT INTO help_conversations (user_id) VALUES (?)");
                        mysqli_stmt_bind_param($stmt, "i", $userId);
                        mysqli_stmt_execute($stmt);
                        $conversationId = mysqli_insert_id($link);
                        mysqli_stmt_close($stmt);
                    }

                    // Save user message
                    $stmt = mysqli_prepare($link, "INSERT INTO help_messages (conversation_id, sender, message_text) VALUES (?, 'user', ?)");
                    mysqli_stmt_bind_param($stmt, "is", $conversationId, $message);
                    mysqli_stmt_execute($stmt);
                    mysqli_stmt_close($stmt);

                    // Get conversation history for context
                    $stmt = mysqli_prepare($link, "SELECT sender, message_text FROM help_messages WHERE conversation_id = ? ORDER BY created_at ASC");
                    mysqli_stmt_bind_param($stmt, "i", $conversationId);
                    mysqli_stmt_execute($stmt);
                    $result = mysqli_stmt_get_result($stmt);
                    $messages = mysqli_fetch_all($result, MYSQLI_ASSOC);
                    mysqli_stmt_close($stmt);

                    foreach ($messages as $msg) {
                        $context[] = [
                            'role' => $msg['sender'] === 'user' ? 'user' : 'assistant',
                            'content' => $msg['message_text']
                        ];
                    }
                } catch (Exception $e) {
                    StripeLogger::log(StripeLogLevel::ERROR, 'Database error in send_help', ['error' => $e->getMessage()]);
                }

                // Get user's subscription level and validate with Stripe subscription status
                $query = "SELECT u.subscription_level, s.status as stripe_status
                          FROM users u
                          LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                              AND s.status IN ('active', 'trialing')
                          WHERE u.id = ?
                          ORDER BY s.created_at DESC
                          LIMIT 1";
                $stmt = mysqli_prepare($link, $query);
                if ($stmt) {
                    mysqli_stmt_bind_param($stmt, "i", $userId);
                    mysqli_stmt_execute($stmt);
                    $result = mysqli_stmt_get_result($stmt);
                    $user = mysqli_fetch_assoc($result);
                    $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
                    $stripeStatus = $user['stripe_status'];

                    // Check if user has paid subscription (basic, advance, premium) with active Stripe status
                    if (
                        $userSubscriptionLevel !== 'free' && !empty($stripeStatus) &&
                        in_array($userSubscriptionLevel, ['basic', 'advance', 'premium'])
                    ) {
                        $webhookUrl = $n8nHelpProWebhookUrl;
                        $isPaidUser = true;
                    }
                    mysqli_stmt_close($stmt);
                }
            } else {
                // Non-logged-in user: use context from frontend if provided
                $context = isset($data['context']) && is_array($data['context']) ? $data['context'] : [];
            }

            // Check webhook URL availability
            if (empty($webhookUrl)) {
                $response = new ErrorResult('Help service not configured');
                $response->send(503);
                return;
            }

            if (!class_exists('N8NSender')) {
                $response = new ErrorResult('Help service not available');
                $response->send(503);
                return;
            }

            try {
                $n8n = new N8NSender($webhookUrl);
                $n8nResp = $n8n->sendToN8N('help_message', [
                    'userId' => $userId,
                    'message' => $message,
                    'context' => $context,
                    'isPaidUser' => $isPaidUser
                ]);

                if ($n8nResp['success'] === true) {
                    $raw = $n8nResp['response'];
                    $decoded = json_decode($raw, true);
                    $responseText = is_array($decoded) ? ($decoded['response'] ?? $decoded['reply'] ?? $decoded['message'] ?? (string)$raw) : (string)$raw;

                    // Save AI response for logged-in users
                    if ($userId && $conversationId && $responseText) {
                        try {
                            $stmt = mysqli_prepare($link, "INSERT INTO help_messages (conversation_id, sender, message_text) VALUES (?, 'ai', ?)");
                            mysqli_stmt_bind_param($stmt, "is", $conversationId, $responseText);
                            mysqli_stmt_execute($stmt);
                            mysqli_stmt_close($stmt);
                        } catch (Exception $e) {
                            StripeLogger::log(StripeLogLevel::ERROR, 'Failed to save AI response', ['error' => $e->getMessage()]);
                        }
                    }

                    $payload = ['response' => $responseText];
                    if ($conversationId) {
                        $payload['conversation_id'] = $conversationId;
                    }
                    (new SuccessResult($payload))->send(200);
                    return;
                } else {
                    StripeLogger::log(StripeLogLevel::WARNING, 'N8N help forwarding failed', [
                        'http_code' => $n8nResp['http_code'] ?? null,
                        'response' => $n8nResp['response'] ?? null,
                        'webhook_url' => $isPaidUser ? 'pro' : 'free'
                    ]);
                    $response = new ErrorResult('Help service temporarily unavailable');
                    $response->send(503);
                    return;
                }
            } catch (Exception $e) {
                StripeLogger::log(StripeLogLevel::ERROR, 'N8N help exception', [
                    'error' => $e->getMessage(),
                    'webhook_url' => $isPaidUser ? 'pro' : 'free'
                ]);
                $response = new ErrorResult('Help service error: ' . $e->getMessage());
                $response->send(503);
                return;
            }
        }

    case 'generate_manual_portfolio': {
            if (!$userId) {
                global $selectedLanguage, $clientMessages;
                $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
                $response = new ErrorResult($clientMessages[$lang]['unauthorized_access'] ?? 'Unauthorized access');
                $response->send(401);
                return;
            }

            // Build AI prompt sentence from incoming parameters and forward to N8N
            $investmentBudget = $data['investmentBudget'] ?? null;
            $budgetCategory = isset($data['budgetCategory']) ? trim((string)$data['budgetCategory']) : '';
            $riskProfile = isset($data['riskProfile']) ? trim((string)$data['riskProfile']) : '';
            $investmentTimeframe = isset($data['investmentTimeframe']) ? trim((string)$data['investmentTimeframe']) : '';
            $selectedCategories = isset($data['selectedCategories']) && is_array($data['selectedCategories']) ? $data['selectedCategories'] : [];

            // Validate required fields (budgetCategory, riskProfile, investmentTimeframe)
            global $selectedLanguage, $clientMessages;
            $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
            if ($budgetCategory === '' || $riskProfile === '' || $investmentTimeframe === '') {
                (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
                return;
            }

            // Helper to format categories with Turkish 've'
            $join_with_ve = function (array $items): string {
                $items = array_values(array_filter(array_map('strval', $items), fn($x) => trim($x) !== ''));
                $count = count($items);
                if ($count === 0) return '';
                if ($count === 1) return $items[0];
                if ($count === 2) return $items[0] . ' ve ' . $items[1];
                $last = array_pop($items);
                return implode(', ', $items) . ' ve ' . $last;
            };

            // Build sentence parts
            $parts = [];
            if ($investmentBudget !== null && is_numeric($investmentBudget) && (float)$investmentBudget > 0) {
                $formatted = number_format((float)$investmentBudget, 0, ',', '.');
                $parts[] = 'yaklaşık ' . $formatted . ' bütçem var';
            }
            $parts[] = 'bütçe kategorim ' . $budgetCategory;
            $parts[] = 'risk profilim ' . $riskProfile;
            $parts[] = 'yatırım vadem ' . $investmentTimeframe;

            $sentence = ucfirst(implode(', ', $parts));
            if (!empty($selectedCategories)) {
                $cats = $join_with_ve($selectedCategories);
                $sentence .= ' ve ' . $cats . ' kategorilerinden seçili coinleri içeren bir portföy template oluştur.';
            } else {
                $sentence .= ' ve seçili coinleri içeren bir portföy template oluştur.';
            }

            if (!empty($n8nManuelInputWebhookUrl) && class_exists('N8NSender')) {
                try {
                    $n8n = new N8NSender($n8nManuelInputWebhookUrl);
                    $n8nResp = $n8n->sendToN8N('generate_manual_portfolio', [
                        'userId' => $userId,
                        'input' => $sentence,
                    ]);

                    if ($n8nResp['success'] === true) {
                        $raw = html_entity_decode($n8nResp['response'], ENT_QUOTES, 'UTF-8');
                        $decoded = json_decode($raw, true);

                        if (is_array($decoded)) {
                            // N8N'den gelen array formatını kontrol et
                            if (isset($decoded[0]) && isset($decoded[0]['assets']) && isset($decoded[0]['risk_level'])) {
                                // Portfolio verisi var, template formatına çevir
                                $portfolioData = $decoded[0];
                                $templateData = convertN8NToTemplate($portfolioData);
                                (new SuccessResult($templateData))->send(200);
                                return;
                            } else {
                                // Başka bir array formatı
                                (new SuccessResult($decoded))->send(200);
                                return;
                            }
                        } else {
                            (new SuccessResult(['message' => (string)$raw]))->send(200);
                            return;
                        }
                    } else {
                        StripeLogger::log(StripeLogLevel::WARNING, 'N8N manual portfolio call failed in generator.php', [
                            'http_code' => $n8nResp['http_code'] ?? null,
                            'response' => $n8nResp['response'] ?? null,
                        ]);
                        (new ErrorResult('Upstream service error'))->send(502);
                        return;
                    }
                } catch (Throwable $e) {
                    StripeLogger::log(StripeLogLevel::ERROR, 'N8N manual portfolio exception in generator.php', [
                        'error' => $e->getMessage(),
                    ]);
                    (new ErrorResult('Service error'))->send(500);
                    return;
                }
            }
            (new ErrorResult('Service unavailable'))->send(503);
            return;
        }
}
