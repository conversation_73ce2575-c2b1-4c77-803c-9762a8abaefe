<?php

// Predefined Portfolio Templates API
// Returns predefined portfolio templates that users can select and create portfolios from

require_once 'utils.php';
require_once 'models/ResultModel.php';
require_once 'config.php';
require_once 'language_config.php';
$selectedLanguage = getSelectedLanguage();
require_once 'language_strings.php';
require_once 'clientmethods/PriceService.php';

// Apply CORS headers
cors_client();

header('Content-Type: application/json');

// Only allow GET for retrieving templates
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response = new ErrorResult('Method not allowed');
    $response->send(405);
    return;
}

// Optional authentication - templates are public but user context may be useful
$userId = authenticate_user(false); // false = don't require auth

try {
    global $link;

    // Get predefined templates with their assets
    $stmt = mysqli_prepare($link, "
        SELECT
            pt.id,
            pt.name,
            pt.description,
            pt.risk_level,
            pt.timeframe,
            pt.strategy,
            pt.suitable_for,
            pt.not_suitable_for
        FROM predefined_portfolio_templates pt
        WHERE pt.is_active = 1
        ORDER BY pt.risk_level, pt.name
    ");

    if (!$stmt) {
        throw new Exception('Failed to prepare templates query');
    }

    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $templates = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $templateId = (int)$row['id'];

        // Get assets for this template
        $assetStmt = mysqli_prepare($link, "
            SELECT
                pta.coin_id,
                pta.percentage,
                c.symbol,
                c.name as full_name,
                c.total_score,
                c.marketcap,
                c.geckoslug,
                c.geckoid
            FROM predefined_template_assets pta
            JOIN coindata c ON c.id = pta.coin_id
            WHERE pta.template_id = ?
            ORDER BY pta.percentage DESC
        ");

        if ($assetStmt) {
            mysqli_stmt_bind_param($assetStmt, 'i', $templateId);
            mysqli_stmt_execute($assetStmt);
            $assetResult = mysqli_stmt_get_result($assetStmt);

            $allocation = [];
            $totalScore = 0;
            $assetCount = 0;

            while ($assetRow = mysqli_fetch_assoc($assetResult)) {
                // Current price bilgisini al
                $currentPrice = 0;
                if (!empty($assetRow['geckoslug'])) {
                    $currentPrice = get_current_price_by_coin_id($assetRow['geckoslug'], ['isGeckoSlug' => true]);
                }

                // Coin kategorisini al
                $category = null;
                if (!empty($assetRow['geckoid'])) {
                    $categoryStmt = mysqli_prepare($link, "SELECT category_name FROM coindata_categories WHERE geckoid = ? LIMIT 1");
                    if ($categoryStmt) {
                        mysqli_stmt_bind_param($categoryStmt, "s", $assetRow['geckoid']);
                        mysqli_stmt_execute($categoryStmt);
                        $categoryResult = mysqli_stmt_get_result($categoryStmt);
                        $categoryRow = mysqli_fetch_assoc($categoryResult);
                        if ($categoryRow) {
                            $category = $categoryRow['category_name'];
                        }
                        mysqli_stmt_close($categoryStmt);
                    }
                }

                $coinScore = $assetRow['total_score'] ? (float)$assetRow['total_score'] : 0;

                $allocation[] = [
                    'id' => (int)$assetRow['coin_id'],
                    'symbol' => $assetRow['symbol'],
                    'fullName' => $assetRow['full_name'],
                    'percentage' => (float)$assetRow['percentage'],
                    'score' => $coinScore > 0 ? $coinScore : null,
                    'currentPrice' => $currentPrice > 0 ? $currentPrice : null,
                    'marketCap' => $assetRow['marketcap'] ? (float)$assetRow['marketcap'] : null,
                    'category' => $category,
                    'description' => ($category ? $category . ' kategorisinde ' : '') . 'önceden tanımlanmış portföy bileşeni'
                ];

                // Toplam skor hesaplama için
                if ($coinScore > 0) {
                    $totalScore += $coinScore * ($assetRow['percentage'] / 100); // Yüzdeye göre ağırlıklı
                    $assetCount++;
                }
            }
            mysqli_stmt_close($assetStmt);
        } else {
            $allocation = [];
        }

        // Metrics hesapla
        $metrics = [
            'totalScore' => $totalScore > 0 ? round($totalScore, 2) : null,
            'assetCount' => count($allocation),
            'averageScore' => $assetCount > 0 ? round($totalScore / $assetCount, 2) : null,
            'diversification' => count($allocation) >= 5 ? 'high' : (count($allocation) >= 3 ? 'medium' : 'low')
        ];

        $templates[] = [
            'id' => (string)$templateId,
            'name' => $row['name'],
            'description' => $row['description'],
            'riskLevel' => $row['risk_level'],
            'timeframe' => $row['timeframe'],
            'strategy' => $row['strategy'],
            'totalScore' => $totalScore > 0 ? round($totalScore, 2) : null,
            'metrics' => $metrics,
            'allocation' => $allocation,
            'suitableFor' => json_decode($row['suitable_for'] ?? '[]', true),
            'notSuitableFor' => json_decode($row['not_suitable_for'] ?? '[]', true),
            'reasoningExplanation' => $totalScore > 0 ?
                "Bu portföy, toplam skor {$metrics['totalScore']} ile {$metrics['assetCount']} farklı varlık arasında dengeli dağılım sunuyor." :
                "Bu portföy, {$metrics['assetCount']} farklı varlık arasında dengeli dağılım sunuyor.",
            'userContext' => ucfirst($row['risk_level']) . ' risk toleranslı yatırımcılar için önceden hazırlanmış portföy'
        ];
    }

    mysqli_stmt_close($stmt);

    $response = [
        'templates' => $templates,
        'total' => count($templates)
    ];

    (new SuccessResult($response))->send(200);

} catch (Exception $e) {
    error_log('Predefined templates error: ' . $e->getMessage());
    (new ErrorResult('Failed to retrieve templates'))->send(500);
}